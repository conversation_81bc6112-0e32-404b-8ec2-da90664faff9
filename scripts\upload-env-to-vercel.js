#!/usr/bin/env node

/**
 * Script to upload environment variables from .env file to Vercel
 * Usage: node scripts/upload-env-to-vercel.js
 */

import { readFileSync } from 'fs';
import { execSync } from 'child_process';
import path from 'path';

const ENV_FILE = '.env';

function parseEnvFile(filePath) {
  try {
    const content = readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const envVars = {};

    for (const line of lines) {
      // Skip comments and empty lines
      if (line.trim() === '' || line.trim().startsWith('#')) {
        continue;
      }

      // Parse KEY=VALUE format
      const match = line.match(/^([A-Z_][A-Z0-9_]*)=(.*)$/);
      if (match) {
        const [, key, value] = match;
        // Remove quotes if present
        const cleanValue = value.replace(/^["']|["']$/g, '');
        
        // Skip placeholder values
        if (!cleanValue.includes('your-') && 
            !cleanValue.includes('sk-your-') && 
            !cleanValue.includes('r8_your-') &&
            cleanValue !== 'your-session-secret-key' &&
            cleanValue !== 'development') {
          envVars[key] = cleanValue;
        }
      }
    }

    return envVars;
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error.message);
    return {};
  }
}

function uploadToVercel(envVars) {
  console.log('🚀 Uploading environment variables to Vercel...\n');

  let successCount = 0;
  let errorCount = 0;

  for (const [key, value] of Object.entries(envVars)) {
    try {
      console.log(`📤 Uploading ${key}...`);
      
      // Use vercel env add command
      const command = `pnpm vercel env add ${key} production`;
      
      // Execute the command and provide the value via stdin
      execSync(command, {
        input: value,
        stdio: ['pipe', 'pipe', 'pipe'],
        encoding: 'utf8'
      });
      
      console.log(`✅ ${key} uploaded successfully`);
      successCount++;
    } catch (error) {
      console.error(`❌ Failed to upload ${key}:`, error.message);
      errorCount++;
    }
  }

  console.log(`\n📊 Upload Summary:`);
  console.log(`✅ Success: ${successCount}`);
  console.log(`❌ Errors: ${errorCount}`);
  
  if (successCount > 0) {
    console.log('\n🔄 Remember to redeploy your application for changes to take effect:');
    console.log('pnpm vercel --prod');
  }
}

function main() {
  console.log('🔧 Vercel Environment Variables Upload Tool\n');

  if (!readFileSync(ENV_FILE, 'utf8')) {
    console.error(`❌ ${ENV_FILE} file not found!`);
    process.exit(1);
  }

  const envVars = parseEnvFile(ENV_FILE);
  
  if (Object.keys(envVars).length === 0) {
    console.log('⚠️  No valid environment variables found to upload.');
    return;
  }

  console.log(`📋 Found ${Object.keys(envVars).length} environment variables to upload:`);
  Object.keys(envVars).forEach(key => {
    console.log(`  - ${key}`);
  });
  
  console.log('\n⚠️  This will upload the following variables to Vercel production environment.');
  console.log('Make sure you want to proceed!\n');

  uploadToVercel(envVars);
}

main();
