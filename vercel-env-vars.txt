# 关键环境变量 - 复制到 Vercel 仪表板
# 访问: https://vercel.com/borealbit/remix-vercel-neon-starter/settings/environment-variables

# 数据库配置
DATABASE_URL=postgresql://remix-vercel-neon-db_owner:<EMAIL>/remix-vercel-neon-db?sslmode=require

# Google OAuth 配置
GOOGLE_CLIENT_ID=982969788186-u74stinsjaik77j3jo8dof583t5e57su.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_JWKS_URI=https://www.googleapis.com/oauth2/v3/certs

# Google One Tap 配置
ONE_TAP_ENABLED=true
GOOGLE_ONE_TAP_ENABLED=true

# 认证配置
AUTH_COOKIE_KEY=Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0=

# Neon Auth 配置
VITE_STACK_PROJECT_ID=25a32b70-91c7-4473-b7a7-b5449375668f
VITE_STACK_PUBLISHABLE_CLIENT_KEY=pck_rkm5fftfasx3skazznhgw4ar92rs7afjm5m6b66kd3dfg
STACK_SECRET_SERVER_KEY=ssk_svq9mtfzcha1b9dh1hkeb5gstekdwbmwwzrzm0jdtmj3g

# Google Analytics
GA_TRACKING_ID=G-JWVEG9DQVX

# 应用配置
NODE_ENV=production
APP_URL=https://remix-vercel-neon-starter-iotp0tria-borealbit.vercel.app

# 安全配置
ALLOWED_ORIGINS=https://remix-vercel-neon-starter-iotp0tria-borealbit.vercel.app
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_WINDOW_MS=60000
ENABLE_SECURITY_HEADERS=true
ENABLE_CSRF_PROTECTION=true

# 功能开关
ENABLE_AI_FEATURES=true
ENABLE_PAYMENT_FEATURES=true
ENABLE_ANALYTICS=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_FILE_UPLOADS=true
ENABLE_BETA_FEATURES=false
ENABLE_EXPERIMENTAL_FEATURES=false

# 日志配置
LOG_LEVEL=info
ENABLE_QUERY_LOGGING=false
ENABLE_SLOW_QUERY_LOGGING=true
SLOW_QUERY_THRESHOLD=1000
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_WEB_VITALS_TRACKING=true
